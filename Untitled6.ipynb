import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from scipy import stats
from scipy.interpolate import griddata
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

# Set publication-ready style
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")
plt.rcParams.update({
    'font.size': 12,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 11,
    'figure.titlesize': 16,
    'font.family': 'DejaVu Sans',
    'axes.grid': True,
    'grid.alpha': 0.3,
    'axes.spines.top': False,
    'axes.spines.right': False
})

class AdvancedSensitivityAnalysis:
    def __init__(self):
        """Initialize the advanced sensitivity analysis framework"""
        # Define parameter ranges and physical meanings
        self.params = {
            'Tfeed': {
                'min': 105, 'max': 110,
                'name': 'Feed Temperature',
                'unit': '°C',
                'description': 'Inlet feed temperature'
            },
            'Xfeed': {
                'min': 0.14, 'max': 0.19,
                'name': 'Feed Composition',
                'unit': 'mol fraction',
                'description': 'Molar composition of key component'
            },
            'Ffeed': {
                'min': 41.2, 'max': 58,
                'name': 'Feed Flow Rate',
                'unit': 'kmol/h',
                'description': 'Volumetric feed flow rate'
            },
            'Tsteam': {
                'min': 125, 'max': 127,
                'name': 'Steam Temperature',
                'unit': '°C',
                'description': 'Heating steam temperature'
            }
        }

        # Calculate midpoints and ranges
        self.midpoints = {param: (values['min'] + values['max']) / 2
                         for param, values in self.params.items()}
        self.ranges = {param: values['max'] - values['min']
                      for param, values in self.params.items()}

        # Analysis parameters
        self.n_steps = 100  # Increased for smoother curves
        self.n_samples_monte_carlo = 10000  # For comprehensive correlation analysis

        # Results storage
        self.results = {}
        self.monte_carlo_data = None
        self.statistical_tests = {}

    def calculate_y(self, Tfeed, Xfeed, Ffeed, Tsteam):
        """
        Calculate output Y using the process equation
        Y = (Ffeed × Xfeed) + (Tsteam − Tfeed)
        """
        return (Ffeed * Xfeed) + (Tsteam - Tfeed)

    def calculate_sensitivity_indices(self, param_name):
        """Calculate comprehensive sensitivity indices"""
        param_range = np.linspace(
            self.params[param_name]['min'],
            self.params[param_name]['max'],
            self.n_steps
        )

        base_values = self.midpoints.copy()
        y_values = []

        for value in param_range:
            base_values[param_name] = value
            y = self.calculate_y(**base_values)
            y_values.append(y)

        y_values = np.array(y_values)

        # Calculate multiple sensitivity metrics
        total_change = y_values.max() - y_values.min()
        relative_change = total_change / np.mean(y_values) * 100  # Percentage
        gradient = np.gradient(y_values, param_range)
        avg_gradient = np.mean(np.abs(gradient))
        max_gradient = np.max(np.abs(gradient))

        # Normalized sensitivity index
        param_range_norm = (self.params[param_name]['max'] - self.params[param_name]['min'])
        sensitivity_index = total_change / param_range_norm

        # First-order Sobol index approximation
        y_variance = np.var(y_values)
        total_variance = self.calculate_total_variance()
        sobol_index = y_variance / total_variance if total_variance > 0 else 0

        # Statistical measures
        linearity_r2 = r2_score(param_range, y_values)

        self.results[param_name] = {
            'param_values': param_range,
            'y_values': y_values,
            'gradient': gradient,
            'total_change': total_change,
            'relative_change': relative_change,
            'avg_gradient': avg_gradient,
            'max_gradient': max_gradient,
            'sensitivity_index': sensitivity_index,
            'sobol_index': sobol_index,
            'linearity_r2': linearity_r2,
            'avg_output': np.mean(y_values),
            'std_output': np.std(y_values),
            'output_range': (y_values.min(), y_values.max()),
            'cv': np.std(y_values) / np.mean(y_values) * 100  # Coefficient of variation
        }

        return param_range, y_values

    def calculate_total_variance(self):
        """Calculate total output variance for Sobol indices"""
        if self.monte_carlo_data is None:
            self.generate_monte_carlo_data()
        return np.var(self.monte_carlo_data['Y'])

    def generate_monte_carlo_data(self):
        """Generate Monte Carlo samples for advanced statistical analysis"""
        np.random.seed(42)

        data = {}
        for param, bounds in self.params.items():
            # Use uniform distribution - can be changed to normal if preferred
            data[param] = np.random.uniform(
                bounds['min'], bounds['max'], self.n_samples_monte_carlo
            )

        # Calculate Y for all combinations
        y_values = []
        for i in range(self.n_samples_monte_carlo):
            y = self.calculate_y(
                data['Tfeed'][i], data['Xfeed'][i],
                data['Ffeed'][i], data['Tsteam'][i]
            )
            y_values.append(y)

        data['Y'] = np.array(y_values)
        self.monte_carlo_data = pd.DataFrame(data)

        return self.monte_carlo_data

    def run_comprehensive_analysis(self):
        """Execute complete sensitivity analysis"""
        print("Running comprehensive sensitivity analysis...")
        print("=" * 60)

        # Generate Monte Carlo data first
        self.generate_monte_carlo_data()

        # Run univariate analyses
        for param in self.params.keys():
            print(f"Analyzing {param} ({self.params[param]['name']})...")
            self.calculate_sensitivity_indices(param)

        # Perform statistical tests
        self.perform_statistical_tests()

        print("Analysis complete!")
        return self.results

    def perform_statistical_tests(self):
        """Perform statistical significance tests"""
        if self.monte_carlo_data is None:
            self.generate_monte_carlo_data()

        for param in self.params.keys():
            # Pearson correlation
            corr_coef, p_value = stats.pearsonr(
                self.monte_carlo_data[param],
                self.monte_carlo_data['Y']
            )

            # Spearman correlation (non-parametric)
            spearman_coef, spearman_p = stats.spearmanr(
                self.monte_carlo_data[param],
                self.monte_carlo_data['Y']
            )

            self.statistical_tests[param] = {
                'pearson_r': corr_coef,
                'pearson_p': p_value,
                'spearman_r': spearman_coef,
                'spearman_p': spearman_p,
                'significant': p_value < 0.05
            }

    def create_publication_plots(self):
        """Create publication-ready sensitivity plots"""
        # Create a comprehensive figure with subplots
        fig = plt.figure(figsize=(20, 16))
        gs = fig.add_gridspec(4, 4, height_ratios=[1, 1, 1, 0.8], width_ratios=[1, 1, 1, 1])

        # Individual parameter response curves
        for i, (param, data) in enumerate(self.results.items()):
            row, col = i // 2, (i % 2) * 2
            ax = fig.add_subplot(gs[row, col:col+2])

            # Main response curve
            ax.plot(data['param_values'], data['y_values'],
                   linewidth=3, alpha=0.8, label='Response Curve')

            # Gradient visualization
            ax2 = ax.twinx()
            ax2.plot(data['param_values'], data['gradient'],
                    color='red', alpha=0.6, linestyle='--',
                    linewidth=2, label='Gradient (dY/dx)')

            # Formatting
            ax.set_xlabel(f"{self.params[param]['name']} ({self.params[param]['unit']})")
            ax.set_ylabel('Output Y', color='blue')
            ax2.set_ylabel('Gradient', color='red')
            ax.set_title(f'{param}: Sensitivity Analysis\n'
                        f'SI = {data["sensitivity_index"]:.3f}, R² = {data["linearity_r2"]:.3f}',
                        fontweight='bold')

            # Add confidence intervals (approximation)
            std_dev = data['std_output']
            ax.fill_between(data['param_values'],
                           data['y_values'] - std_dev/2,
                           data['y_values'] + std_dev/2,
                           alpha=0.2, label='±σ/2 Band')

            ax.grid(True, alpha=0.3)
            ax.legend(loc='upper left')
            ax2.legend(loc='upper right')

        # Summary sensitivity comparison
        ax_summary = fig.add_subplot(gs[3, :])

        params = list(self.results.keys())
        sensitivity_indices = [self.results[p]['sensitivity_index'] for p in params]
        sobol_indices = [self.results[p]['sobol_index'] for p in params]
        r_squared = [self.results[p]['linearity_r2'] for p in params]

        x = np.arange(len(params))
        width = 0.25

        bars1 = ax_summary.bar(x - width, sensitivity_indices, width,
                              label='Sensitivity Index', alpha=0.8, color='skyblue')
        bars2 = ax_summary.bar(x, sobol_indices, width,
                              label='Sobol Index', alpha=0.8, color='lightcoral')
        bars3 = ax_summary.bar(x + width, r_squared, width,
                              label='Linearity (R²)', alpha=0.8, color='lightgreen')

        # Add value labels
        for bars in [bars1, bars2, bars3]:
            for bar in bars:
                height = bar.get_height()
                ax_summary.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                               f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        ax_summary.set_xlabel('Parameters')
        ax_summary.set_ylabel('Index Value')
        ax_summary.set_title('Comparative Sensitivity Analysis', fontweight='bold')
        ax_summary.set_xticks(x)
        ax_summary.set_xticklabels([f"{p}\n({self.params[p]['unit']})" for p in params])
        ax_summary.legend()
        ax_summary.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('sensitivity_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    def create_advanced_correlation_analysis(self):
        """Create advanced correlation and interaction plots"""
        if self.monte_carlo_data is None:
            self.generate_monte_carlo_data()

        # Create comprehensive correlation figure
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 1. Enhanced correlation matrix
        ax1 = axes[0, 0]
        corr_matrix = self.monte_carlo_data.corr()
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))

        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdBu_r',
                   center=0, square=True, linewidths=0.5, ax=ax1,
                   cbar_kws={"shrink": .8}, fmt='.3f')
        ax1.set_title('Correlation Matrix\n(Pearson)', fontweight='bold')

        # 2. Partial correlation analysis
        ax2 = axes[0, 1]
        # Calculate partial correlations (simplified approach)
        y_values = self.monte_carlo_data['Y'].values
        param_names = ['Tfeed', 'Xfeed', 'Ffeed', 'Tsteam']
        partial_corrs = []

        for param in param_names:
            x = self.monte_carlo_data[param].values
            corr, _ = stats.pearsonr(x, y_values)
            partial_corrs.append(corr)

        colors = ['red' if abs(c) > 0.5 else 'blue' for c in partial_corrs]
        bars = ax2.bar(param_names, partial_corrs, color=colors, alpha=0.7)
        ax2.set_title('Parameter-Output Correlations', fontweight='bold')
        ax2.set_ylabel('Correlation Coefficient')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax2.grid(True, alpha=0.3)

        # Add significance indicators
        for i, (bar, param) in enumerate(zip(bars, param_names)):
            if self.statistical_tests[param]['significant']:
                ax2.text(bar.get_x() + bar.get_width()/2.,
                        bar.get_height() + 0.02, '*',
                        ha='center', va='bottom', fontsize=16, color='red')

        # 3. Sensitivity tornado plot
        ax3 = axes[0, 2]
        params = list(self.results.keys())
        sensitivities = [self.results[p]['sensitivity_index'] for p in params]
        colors_tornado = plt.cm.RdYlBu_r(np.linspace(0.2, 0.8, len(params)))

        y_pos = np.arange(len(params))
        bars = ax3.barh(y_pos, sensitivities, color=colors_tornado, alpha=0.8)
        ax3.set_yticks(y_pos)
        ax3.set_yticklabels([f"{p}\n({self.params[p]['unit']})" for p in params])
        ax3.set_xlabel('Sensitivity Index')
        ax3.set_title('Tornado Plot\n(Parameter Importance)', fontweight='bold')
        ax3.grid(True, alpha=0.3, axis='x')

        # Add value labels
        for i, (bar, val) in enumerate(zip(bars, sensitivities)):
            ax3.text(val + 0.001, bar.get_y() + bar.get_height()/2,
                    f'{val:.3f}', va='center', ha='left', fontsize=10)

        # 4. Scatter plot matrix (selected pairs)
        ax4 = axes[1, 0]
        # Most influential parameter vs Y
        most_influential = max(params, key=lambda p: self.results[p]['sensitivity_index'])
        ax4.scatter(self.monte_carlo_data[most_influential],
                   self.monte_carlo_data['Y'], alpha=0.5, s=20)
        ax4.set_xlabel(f"{self.params[most_influential]['name']} ({self.params[most_influential]['unit']})")
        ax4.set_ylabel('Output Y')
        ax4.set_title(f'Most Influential: {most_influential}', fontweight='bold')

        # Add regression line
        z = np.polyfit(self.monte_carlo_data[most_influential],
                      self.monte_carlo_data['Y'], 1)
        p = np.poly1d(z)
        ax4.plot(self.monte_carlo_data[most_influential],
                p(self.monte_carlo_data[most_influential]),
                "r--", alpha=0.8, linewidth=2)
        ax4.grid(True, alpha=0.3)

        # 5. Residual analysis
        ax5 = axes[1, 1]
        predicted_y = p(self.monte_carlo_data[most_influential])
        residuals = self.monte_carlo_data['Y'] - predicted_y
        ax5.scatter(predicted_y, residuals, alpha=0.5, s=20)
        ax5.axhline(y=0, color='red', linestyle='--', alpha=0.8)
        ax5.set_xlabel('Predicted Y')
        ax5.set_ylabel('Residuals')
        ax5.set_title('Residual Analysis', fontweight='bold')
        ax5.grid(True, alpha=0.3)

        # 6. Distribution of output Y
        ax6 = axes[1, 2]
        ax6.hist(self.monte_carlo_data['Y'], bins=50, alpha=0.7, density=True,
                color='skyblue', edgecolor='black')

        # Overlay normal distribution
        mu, sigma = stats.norm.fit(self.monte_carlo_data['Y'])
        x_norm = np.linspace(self.monte_carlo_data['Y'].min(),
                           self.monte_carlo_data['Y'].max(), 100)
        ax6.plot(x_norm, stats.norm.pdf(x_norm, mu, sigma),
                'r-', linewidth=2, label=f'Normal (μ={mu:.2f}, σ={sigma:.2f})')

        ax6.set_xlabel('Output Y')
        ax6.set_ylabel('Density')
        ax6.set_title('Output Distribution', fontweight='bold')
        ax6.legend()
        ax6.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('advanced_correlation_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    def create_3d_interaction_surfaces(self):
        """Create advanced 3D interaction analysis"""
        param_pairs = [
            ('Tfeed', 'Xfeed'),
            ('Ffeed', 'Xfeed'),
            ('Tsteam', 'Tfeed')
        ]

        fig = plt.figure(figsize=(20, 6))

        for i, (param1, param2) in enumerate(param_pairs):
            ax = fig.add_subplot(1, 3, i+1, projection='3d')

            # Create parameter grids
            p1_range = np.linspace(self.params[param1]['min'],
                                 self.params[param1]['max'], 40)
            p2_range = np.linspace(self.params[param2]['min'],
                                 self.params[param2]['max'], 40)
            P1, P2 = np.meshgrid(p1_range, p2_range)

            # Fix other parameters at midpoints
            fixed_params = {p: self.midpoints[p] for p in self.params.keys()
                           if p not in [param1, param2]}

            # Calculate Y surface
            Y_surface = np.zeros_like(P1)
            for j in range(P1.shape[0]):
                for k in range(P1.shape[1]):
                    params_dict = fixed_params.copy()
                    params_dict[param1] = P1[j, k]
                    params_dict[param2] = P2[j, k]
                    Y_surface[j, k] = self.calculate_y(**params_dict)

            # Create surface plot with enhanced styling
            surf = ax.plot_surface(P1, P2, Y_surface, cmap='viridis',
                                 alpha=0.9, linewidth=0, antialiased=True)

            # Add contour lines at the bottom
            ax.contour(P1, P2, Y_surface, zdir='z',
                      offset=Y_surface.min()-1, cmap='viridis', alpha=0.5)

            ax.set_xlabel(f"{self.params[param1]['name']}\n({self.params[param1]['unit']})")
            ax.set_ylabel(f"{self.params[param2]['name']}\n({self.params[param2]['unit']})")
            ax.set_zlabel('Output Y')
            ax.set_title(f'3D Interaction Surface\n{param1} vs {param2}',
                        fontweight='bold')

            # Improve viewing angle
            ax.view_init(elev=20, azim=45)

        plt.tight_layout()
        plt.savefig('3d_interaction_surfaces.png', dpi=300, bbox_inches='tight')
        plt.show()

    def create_interactive_plotly_dashboard(self):
        """Create interactive Plotly dashboard for web-based analysis"""
        # Create subplots
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=('Parameter Response Curves', 'Sensitivity Comparison',
                           'Correlation Heatmap', '3D Parameter Interaction',
                           'Monte Carlo Scatter', 'Statistical Summary'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"type": "scene"}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )

        # 1. Parameter response curves
        colors = px.colors.qualitative.Set1
        for i, (param, data) in enumerate(self.results.items()):
            fig.add_trace(
                go.Scatter(x=data['param_values'], y=data['y_values'],
                          mode='lines', name=param, line=dict(width=3, color=colors[i])),
                row=1, col=1
            )

        # 2. Sensitivity comparison
        params = list(self.results.keys())
        sensitivity_values = [self.results[p]['sensitivity_index'] for p in params]
        fig.add_trace(
            go.Bar(x=params, y=sensitivity_values,
                  marker_color=colors[:len(params)],
                  name='Sensitivity Index'),
            row=1, col=2
        )

        # 3. Correlation heatmap
        if self.monte_carlo_data is not None:
            corr_matrix = self.monte_carlo_data.corr()
            fig.add_trace(
                go.Heatmap(z=corr_matrix.values,
                          x=corr_matrix.columns,
                          y=corr_matrix.columns,
                          colorscale='RdBu',
                          zmid=0),
                row=2, col=1
            )

        # 4. 3D interaction surface (example with two most influential parameters)
        if len(params) >= 2:
            sorted_params = sorted(params,
                                 key=lambda p: self.results[p]['sensitivity_index'],
                                 reverse=True)
            param1, param2 = sorted_params[:2]

            p1_range = np.linspace(self.params[param1]['min'],
                                 self.params[param1]['max'], 20)
            p2_range = np.linspace(self.params[param2]['min'],
                                 self.params[param2]['max'], 20)
            P1, P2 = np.meshgrid(p1_range, p2_range)

            fixed_params = {p: self.midpoints[p] for p in self.params.keys()
                           if p not in [param1, param2]}

            Y_surface = np.zeros_like(P1)
            for j in range(P1.shape[0]):
                for k in range(P1.shape[1]):
                    params_dict = fixed_params.copy()
                    params_dict[param1] = P1[j, k]
                    params_dict[param2] = P2[j, k]
                    Y_surface[j, k] = self.calculate_y(**params_dict)

            fig.add_trace(
                go.Surface(x=P1, y=P2, z=Y_surface, colorscale='viridis'),
                row=2, col=2
            )

        # 5. Monte Carlo scatter
        if self.monte_carlo_data is not None:
            most_influential = max(params, key=lambda p: self.results[p]['sensitivity_index'])
            fig.add_trace(
                go.Scatter(x=self.monte_carlo_data[most_influential],
                          y=self.monte_carlo_data['Y'],
                          mode='markers', name=f'{most_influential} vs Y',
                          marker=dict(size=3, opacity=0.6)),
                row=3, col=1
            )

        # Update layout
        fig.update_layout(
            height=1200,
            title_text="Advanced Sensitivity Analysis Dashboard",
            title_x=0.5,
            showlegend=True
        )

        # Update axes labels
        fig.update_xaxes(title_text="Parameter Value", row=1, col=1)
        fig.update_yaxes(title_text="Output Y", row=1, col=1)
        fig.update_xaxes(title_text="Parameters", row=1, col=2)
        fig.update_yaxes(title_text="Sensitivity Index", row=1, col=2)

        # Save as HTML
        fig.write_html("interactive_sensitivity_dashboard.html")
        fig.show()

    def generate_comprehensive_report(self):
        """Generate a comprehensive statistical and technical report"""
        report = []
        report.append("=" * 80)
        report.append("COMPREHENSIVE SENSITIVITY ANALYSIS REPORT")
        report.append("=" * 80)
        report.append(f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"Monte Carlo Samples: {self.n_samples_monte_carlo:,}")
        report.append(f"Univariate Steps: {self.n_steps}")
        report.append("")

        # Process equation
        report.append("PROCESS EQUATION:")
        report.append("Y = (Ffeed × Xfeed) + (Tsteam − Tfeed)")
        report.append("")

        # Parameter summary
        report.append("PARAMETER SPECIFICATIONS:")
        report.append("-" * 50)
        for param, info in self.params.items():
            report.append(f"{param:8s}: {info['min']:6.2f} - {info['max']:6.2f} {info['unit']:10s} | {info['description']}")
        report.append("")

        # Statistical summary
        if self.monte_carlo_data is not None:
            y_stats = self.monte_carlo_data['Y'].describe()
            report.append("OUTPUT STATISTICS (Monte Carlo Analysis):")
            report.append("-" * 50)
            report.append(f"Mean (μ):       {y_stats['mean']:10.4f}")
            report.append(f"Std Dev (σ):    {y_stats['std']:10.4f}")
            report.append(f"Minimum:        {y_stats['min']:10.4f}")
            report.append(f"Maximum:        {y_stats['max']:10.4f}")
            report.append(f"Range:          {y_stats['max'] - y_stats['min']:10.4f}")
            report.append(f"CV (%):         {(y_stats['std']/y_stats['mean']*100):10.2f}")
            report.append("")

        # Sensitivity ranking
        sorted_params = sorted(self.results.items(),
                             key=lambda x: x[1]['sensitivity_index'],
                             reverse=True)

        report.append("PARAMETER SENSITIVITY RANKING:")
        report.append("-" * 50)
        report.append(f"{'Rank':<4} {'Parameter':<8} {'Sens.Index':<12} {'R²':<8} {'Correlation':<12} {'P-value':<10}")
        report.append("-" * 50)

        for i, (param, data) in enumerate(sorted_params, 1):
            corr = self.statistical_tests[param]['pearson_r']
            p_val = self.statistical_tests[param]['pearson_p']
            significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else ""

            report.append(f"{i:<4} {param:<8} {data['sensitivity_index']:<12.4f} "
                         f"{data['linearity_r2']:<8.4f} {corr:<12.4f} "
                         f"{p_val:<10.4e} {significance}")

        report.append("")
        report.append("Significance levels: *** p<0.001, ** p<0.01, * p<0.05")
        report.append("")

        # Detailed parameter analysis
        report.append("DETAILED PARAMETER ANALYSIS:")
        report.append("=" * 50)

        for param, data in self.results.items():
            report.append(f"\n{param.upper()} ({self.params[param]['name']}):")
            report.append("-" * 30)
            report.append(f"Operating Range:    {self.params[param]['min']} - {self.params[param]['max']} {self.params[param]['unit']}")
            report.append(f"Sensitivity Index:  {data['sensitivity_index']:.4f}")
            report.append(f"Total Change:       {data['total_change']:.4f}")
            report.append(f"Relative Change:    {data['relative_change']:.2f}%")
            report.append(f"Average Gradient:   {data['avg_gradient']:.4f}")
            report.append(f"Linearity (R²):     {data['linearity_r2']:.4f}")
            report.append(f"Correlation:        {self.statistical_tests[param]['pearson_r']:.4f}")
            report.append(f"P-value:            {self.statistical_tests[param]['pearson_p']:.4e}")
            report.append(f"Output Range:       {data['output_range'][0]:.4f} - {data['output_range'][1]:.4f}")
            report.append(f"Coefficient of Var: {data['cv']:.2f}%")

        report.append("")
        report.append("RECOMMENDATIONS:")
        report.append("-" * 50)

        # Generate recommendations based on sensitivity analysis
        most_sensitive = max(self.results.items(), key=lambda x: x[1]['sensitivity_index'])
        least_sensitive = min(self.results.items(), key=lambda x: x[1]['sensitivity_index'])

        report.append(f"1. Most Critical Parameter: {most_sensitive[0]} (SI = {most_sensitive[1]['sensitivity_index']:.4f})")
        report.append(f"   - Requires tight control and monitoring")
        report.append(f"   - Small changes cause significant output variation")
        report.append("")
        report.append(f"2. Least Critical Parameter: {least_sensitive[0]} (SI = {least_sensitive[1]['sensitivity_index']:.4f})")
        report.append(f"   - More tolerance in operational control")
        report.append(f"   - Lower priority for precision instrumentation")
        report.append("")

        # Control recommendations
        for param, data in sorted(self.results.items(), key=lambda x: x[1]['sensitivity_index'], reverse=True):
            if data['sensitivity_index'] > 1.0:
                report.append(f"3. {param}: HIGH PRIORITY CONTROL")
                report.append(f"   - Implement advanced control strategies")
                report.append(f"   - Use high-precision sensors")
                report.append(f"   - Consider cascade or feedforward control")
            elif data['sensitivity_index'] > 0.5:
                report.append(f"4. {param}: MEDIUM PRIORITY CONTROL")
                report.append(f"   - Standard PID control adequate")
                report.append(f"   - Regular calibration recommended")
            else:
                report.append(f"5. {param}: LOW PRIORITY CONTROL")
                report.append(f"   - Basic control sufficient")
                report.append(f"   - Focus on other parameters")
            report.append("")

        report.append("=" * 80)
        report.append("END OF REPORT")
        report.append("=" * 80)

        # Save report to file
        with open('sensitivity_analysis_report.txt', 'w') as f:
            f.write('\n'.join(report))

        # Print report
        for line in report:
            print(line)

        return report

    def create_additional_charts(self):
        \"\"\"Create additional specialized charts for comprehensive analysis\"\"\"
        fig, axes = plt.subplots(3, 3, figsize=(20, 18))
        fig.suptitle('Advanced Sensitivity Analysis - Additional Charts', fontsize=16, fontweight='bold')

        # 1. Parameter Importance Pie Chart
        ax1 = axes[0, 0]
        params = list(self.results.keys())
        sensitivities = [self.results[p]['sensitivity_index'] for p in params]
        colors = plt.cm.Set3(np.linspace(0, 1, len(params)))
        
        wedges, texts, autotexts = ax1.pie(sensitivities, labels=params, autopct='%1.1f%%',
                                          colors=colors, startangle=90, explode=[0.05]*len(params))
        ax1.set_title('Parameter Sensitivity Distribution', fontweight='bold')
        
        # 2. Box Plot of Parameter Variations
        ax2 = axes[0, 1]
        if self.monte_carlo_data is not None:
            param_data = [self.monte_carlo_data[p] for p in params]
            bp = ax2.boxplot(param_data, labels=params, patch_artist=True)
            for patch, color in zip(bp['boxes'], colors):
                patch.set_facecolor(color)
        ax2.set_title('Parameter Value Distributions', fontweight='bold')
        ax2.set_ylabel('Parameter Values')
        ax2.tick_params(axis='x', rotation=45)

        # 3. Gradient Analysis
        ax3 = axes[0, 2]
        for i, (param, data) in enumerate(self.results.items()):
            ax3.plot(data['param_values'], np.abs(data['gradient']), 
                    label=param, linewidth=2, color=colors[i])
        ax3.set_title('Absolute Gradient Analysis', fontweight='bold')
        ax3.set_xlabel('Parameter Value')
        ax3.set_ylabel('|dY/dx|')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. Sensitivity vs Linearity Scatter
        ax4 = axes[1, 0]
        sens_values = [self.results[p]['sensitivity_index'] for p in params]
        r2_values = [self.results[p]['linearity_r2'] for p in params]
        scatter = ax4.scatter(sens_values, r2_values, c=range(len(params)), 
                             cmap='viridis', s=100, alpha=0.7)
        for i, param in enumerate(params):
            ax4.annotate(param, (sens_values[i], r2_values[i]), 
                        xytext=(5, 5), textcoords='offset points')
        ax4.set_xlabel('Sensitivity Index')
        ax4.set_ylabel('Linearity (R²)')
        ax4.set_title('Sensitivity vs Linearity', fontweight='bold')
        ax4.grid(True, alpha=0.3)

        # 5. Output Variance Decomposition
        ax5 = axes[1, 1]
        sobol_indices = [self.results[p]['sobol_index'] for p in params]
        bars = ax5.bar(params, sobol_indices, color=colors, alpha=0.8)
        ax5.set_title('Sobol Sensitivity Indices', fontweight='bold')
        ax5.set_ylabel('Sobol Index')
        ax5.tick_params(axis='x', rotation=45)
        
        # Add value labels on bars
        for bar, val in zip(bars, sobol_indices):
            ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                    f'{val:.3f}', ha='center', va='bottom')

        # 6. Parameter Correlation Network
        ax6 = axes[1, 2]
        if self.monte_carlo_data is not None:
            corr_matrix = self.monte_carlo_data[params].corr()
            im = ax6.imshow(corr_matrix, cmap='RdBu_r', vmin=-1, vmax=1)
            ax6.set_xticks(range(len(params)))
            ax6.set_yticks(range(len(params)))
            ax6.set_xticklabels(params, rotation=45)
            ax6.set_yticklabels(params)
            
            # Add correlation values
            for i in range(len(params)):
                for j in range(len(params)):
                    text = ax6.text(j, i, f'{corr_matrix.iloc[i, j]:.2f}',
                                   ha='center', va='center', color='black')
        ax6.set_title('Parameter Correlation Matrix', fontweight='bold')
        plt.colorbar(im, ax=ax6, shrink=0.8)

        # 7. Cumulative Sensitivity
        ax7 = axes[2, 0]
        sorted_params = sorted(params, key=lambda p: self.results[p]['sensitivity_index'], reverse=True)
        sorted_sens = [self.results[p]['sensitivity_index'] for p in sorted_params]
        cumulative_sens = np.cumsum(sorted_sens) / np.sum(sorted_sens) * 100
        
        ax7.plot(range(1, len(sorted_params)+1), cumulative_sens, 'o-', linewidth=3, markersize=8)
        ax7.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='80% Threshold')
        ax7.set_xlabel('Parameter Rank')
        ax7.set_ylabel('Cumulative Sensitivity (%)')
        ax7.set_title('Cumulative Sensitivity Analysis', fontweight='bold')
        ax7.set_xticks(range(1, len(sorted_params)+1))
        ax7.set_xticklabels(sorted_params, rotation=45)
        ax7.legend()
        ax7.grid(True, alpha=0.3)

        # 8. Statistical Significance Plot
        ax8 = axes[2, 1]
        p_values = [self.statistical_tests[p]['pearson_p'] for p in params]
        correlations = [self.statistical_tests[p]['pearson_r'] for p in params]
        
        # Color by significance
        colors_sig = ['red' if p < 0.001 else 'orange' if p < 0.01 else 'yellow' if p < 0.05 else 'gray' 
                     for p in p_values]
        
        bars = ax8.bar(params, np.abs(correlations), color=colors_sig, alpha=0.8)
        ax8.axhline(y=0.5, color='red', linestyle='--', alpha=0.7, label='Strong Correlation')
        ax8.set_title('Statistical Significance of Correlations', fontweight='bold')
        ax8.set_ylabel('|Correlation Coefficient|')
        ax8.tick_params(axis='x', rotation=45)
        ax8.legend()
        ax8.grid(True, alpha=0.3)

        # 9. Risk Assessment Matrix
        ax9 = axes[2, 2]
        impact = [self.results[p]['relative_change'] for p in params]
        uncertainty = [self.results[p]['cv'] for p in params]  # Coefficient of variation as uncertainty
        
        scatter = ax9.scatter(uncertainty, impact, c=sens_values, cmap='Reds', s=150, alpha=0.8)
        for i, param in enumerate(params):
            ax9.annotate(param, (uncertainty[i], impact[i]), 
                        xytext=(5, 5), textcoords='offset points')
        
        ax9.axhline(y=np.mean(impact), color='blue', linestyle='--', alpha=0.5, label='Mean Impact')
        ax9.axvline(x=np.mean(uncertainty), color='blue', linestyle='--', alpha=0.5, label='Mean Uncertainty')
        ax9.set_xlabel('Uncertainty (CV %)')
        ax9.set_ylabel('Impact (Relative Change %)')
        ax9.set_title('Risk Assessment Matrix', fontweight='bold')
        ax9.legend()
        ax9.grid(True, alpha=0.3)
        plt.colorbar(scatter, ax=ax9, label='Sensitivity Index')

        plt.tight_layout()
        plt.savefig('additional_sensitivity_charts.png', dpi=300, bbox_inches='tight')
        plt.show()

    def run_complete_analysis(self):
        \"\"\"Run the complete enhanced sensitivity analysis\"\"\"
        print(\"Starting Enhanced Sensitivity Analysis...\")
        print(\"=\" * 60)
        
        # Run comprehensive analysis
        self.run_comprehensive_analysis()
        
        # Create all visualizations
        print(\"\nGenerating publication-ready plots...\")
        self.create_publication_plots()
        
        print(\"\nGenerating advanced correlation analysis...\")
        self.create_advanced_correlation_analysis()
        
        print(\"\nGenerating 3D interaction surfaces...\")
        self.create_3d_interaction_surfaces()
        
        print(\"\nGenerating additional specialized charts...\")
        self.create_additional_charts()
        
        print(\"\nGenerating interactive dashboard...\")
        self.create_interactive_plotly_dashboard()
        
        print(\"\nGenerating comprehensive report...\")
        self.generate_comprehensive_report()
        
        print(\"\nAnalysis Complete! Files generated:\")  
        print(\"- sensitivity_analysis.png\")
        print(\"- advanced_correlation_analysis.png\")
        print(\"- 3d_interaction_surfaces.png\")
        print(\"- additional_sensitivity_charts.png\")
        print(\"- interactive_sensitivity_dashboard.html\")
        print(\"- sensitivity_analysis_report.txt\")
        
        return self.results

# Execute the enhanced analysis
if __name__ == \"__main__\":
    # Create analyzer instance
    analyzer = AdvancedSensitivityAnalysis()
    
    # Run complete analysis
    results = analyzer.run_complete_analysis()
    
    # Display summary
    print(\"\n\" + \"=\" * 60)
    print(\"ANALYSIS SUMMARY\")
    print(\"=\" * 60)
    
    for param, data in sorted(results.items(), key=lambda x: x[1]['sensitivity_index'], reverse=True):
        print(f\"{param:8s}: SI = {data['sensitivity_index']:.4f}, R² = {data['linearity_r2']:.4f}, \"
              f\"Corr = {analyzer.statistical_tests[param]['pearson_r']:.4f}\")