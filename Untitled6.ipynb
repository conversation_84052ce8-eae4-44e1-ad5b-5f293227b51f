{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 111}, "id": "JQS-hqwNkxBJ", "outputId": "053f2f8c-e90d-4678-e9ba-be106d52254d"}, "outputs": [{"output_type": "error", "ename": "SyntaxError", "evalue": "unterminated string literal (detected at line 633) (ipython-input-2-*********.py, line 633)", "traceback": ["\u001b[0;36m  File \u001b[0;32m\"/tmp/ipython-input-2-*********.py\"\u001b[0;36m, line \u001b[0;32m633\u001b[0m\n\u001b[0;31m    report.append(f\"Operating Range:    {self.params[param]['min']} - {self.params[param]['max']} {self.params[param]['unit\u001b[0m\n\u001b[0m                  ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m unterminated string literal (detected at line 633)\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from mpl_toolkits.mplot3d import Axes3D\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "from scipy import stats\n", "from scipy.interpolate import griddata\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import r2_score\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set publication-ready style\n", "plt.style.use('seaborn-v0_8-whitegrid')\n", "sns.set_palette(\"husl\")\n", "plt.rcParams.update({\n", "    'font.size': 12,\n", "    'axes.titlesize': 14,\n", "    'axes.labelsize': 12,\n", "    'xtick.labelsize': 10,\n", "    'ytick.labelsize': 10,\n", "    'legend.fontsize': 11,\n", "    'figure.titlesize': 16,\n", "    'font.family': 'DejaVu Sans',\n", "    'axes.grid': True,\n", "    'grid.alpha': 0.3,\n", "    'axes.spines.top': <PERSON><PERSON><PERSON>,\n", "    'axes.spines.right': False\n", "})\n", "\n", "class AdvancedSensitivityAnalysis:\n", "    def __init__(self):\n", "        \"\"\"Initialize the advanced sensitivity analysis framework\"\"\"\n", "        # Define parameter ranges and physical meanings\n", "        self.params = {\n", "            'Tfeed': {\n", "                'min': 105, 'max': 110,\n", "                'name': 'Feed Temperature',\n", "                'unit': '°C',\n", "                'description': 'Inlet feed temperature'\n", "            },\n", "            'Xfeed': {\n", "                'min': 0.14, 'max': 0.19,\n", "                'name': 'Feed Composition',\n", "                'unit': 'mol fraction',\n", "                'description': 'Molar composition of key component'\n", "            },\n", "            'Ffeed': {\n", "                'min': 41.2, 'max': 58,\n", "                'name': 'Feed Flow Rate',\n", "                'unit': 'kmol/h',\n", "                'description': 'Volumetric feed flow rate'\n", "            },\n", "            'Tsteam': {\n", "                'min': 125, 'max': 127,\n", "                'name': 'Steam Temperature',\n", "                'unit': '°C',\n", "                'description': 'Heating steam temperature'\n", "            }\n", "        }\n", "\n", "        # Calculate midpoints and ranges\n", "        self.midpoints = {param: (values['min'] + values['max']) / 2\n", "                         for param, values in self.params.items()}\n", "        self.ranges = {param: values['max'] - values['min']\n", "                      for param, values in self.params.items()}\n", "\n", "        # Analysis parameters\n", "        self.n_steps = 100  # Increased for smoother curves\n", "        self.n_samples_monte_carlo = 10000  # For comprehensive correlation analysis\n", "\n", "        # Results storage\n", "        self.results = {}\n", "        self.monte_carlo_data = None\n", "        self.statistical_tests = {}\n", "\n", "    def calculate_y(self, <PERSON>feed, <PERSON>feed, <PERSON>feed, T<PERSON><PERSON>):\n", "        \"\"\"\n", "        Calculate output Y using the process equation\n", "        Y = (Ffeed × Xfeed) + (T<PERSON>am − Tfeed)\n", "        \"\"\"\n", "        return (Ffeed * Xfeed) + (Tsteam - Tfeed)\n", "\n", "    def calculate_sensitivity_indices(self, param_name):\n", "        \"\"\"Calculate comprehensive sensitivity indices\"\"\"\n", "        param_range = np.linspace(\n", "            self.params[param_name]['min'],\n", "            self.params[param_name]['max'],\n", "            self.n_steps\n", "        )\n", "\n", "        base_values = self.midpoints.copy()\n", "        y_values = []\n", "\n", "        for value in param_range:\n", "            base_values[param_name] = value\n", "            y = self.calculate_y(**base_values)\n", "            y_values.append(y)\n", "\n", "        y_values = np.array(y_values)\n", "\n", "        # Calculate multiple sensitivity metrics\n", "        total_change = y_values.max() - y_values.min()\n", "        relative_change = total_change / np.mean(y_values) * 100  # Percentage\n", "        gradient = np.gradient(y_values, param_range)\n", "        avg_gradient = np.mean(np.abs(gradient))\n", "        max_gradient = np.max(np.abs(gradient))\n", "\n", "        # Normalized sensitivity index\n", "        param_range_norm = (self.params[param_name]['max'] - self.params[param_name]['min'])\n", "        sensitivity_index = total_change / param_range_norm\n", "\n", "        # First-order Sobol index approximation\n", "        y_variance = np.var(y_values)\n", "        total_variance = self.calculate_total_variance()\n", "        sobol_index = y_variance / total_variance if total_variance > 0 else 0\n", "\n", "        # Statistical measures\n", "        linearity_r2 = r2_score(param_range, y_values)\n", "\n", "        self.results[param_name] = {\n", "            'param_values': param_range,\n", "            'y_values': y_values,\n", "            'gradient': gradient,\n", "            'total_change': total_change,\n", "            'relative_change': relative_change,\n", "            'avg_gradient': avg_gradient,\n", "            'max_gradient': max_gradient,\n", "            'sensitivity_index': sensitivity_index,\n", "            'sobol_index': sobol_index,\n", "            'linearity_r2': linearity_r2,\n", "            'avg_output': np.mean(y_values),\n", "            'std_output': np.std(y_values),\n", "            'output_range': (y_values.min(), y_values.max()),\n", "            'cv': np.std(y_values) / np.mean(y_values) * 100  # Coefficient of variation\n", "        }\n", "\n", "        return param_range, y_values\n", "\n", "    def calculate_total_variance(self):\n", "        \"\"\"Calculate total output variance for Sobol indices\"\"\"\n", "        if self.monte_carlo_data is None:\n", "            self.generate_monte_carlo_data()\n", "        return np.var(self.monte_carlo_data['Y'])\n", "\n", "    def generate_monte_carlo_data(self):\n", "        \"\"\"Generate Monte Carlo samples for advanced statistical analysis\"\"\"\n", "        np.random.seed(42)\n", "\n", "        data = {}\n", "        for param, bounds in self.params.items():\n", "            # Use uniform distribution - can be changed to normal if preferred\n", "            data[param] = np.random.uniform(\n", "                bounds['min'], bounds['max'], self.n_samples_monte_carlo\n", "            )\n", "\n", "        # Calculate Y for all combinations\n", "        y_values = []\n", "        for i in range(self.n_samples_monte_carlo):\n", "            y = self.calculate_y(\n", "                data['Tfeed'][i], data['Xfeed'][i],\n", "                data['Ffeed'][i], data['Tsteam'][i]\n", "            )\n", "            y_values.append(y)\n", "\n", "        data['Y'] = np.array(y_values)\n", "        self.monte_carlo_data = pd.DataFrame(data)\n", "\n", "        return self.monte_carlo_data\n", "\n", "    def run_comprehensive_analysis(self):\n", "        \"\"\"Execute complete sensitivity analysis\"\"\"\n", "        print(\"Running comprehensive sensitivity analysis...\")\n", "        print(\"=\" * 60)\n", "\n", "        # Generate Monte Carlo data first\n", "        self.generate_monte_carlo_data()\n", "\n", "        # Run univariate analyses\n", "        for param in self.params.keys():\n", "            print(f\"Analyzing {param} ({self.params[param]['name']})...\")\n", "            self.calculate_sensitivity_indices(param)\n", "\n", "        # Perform statistical tests\n", "        self.perform_statistical_tests()\n", "\n", "        print(\"Analysis complete!\")\n", "        return self.results\n", "\n", "    def perform_statistical_tests(self):\n", "        \"\"\"Perform statistical significance tests\"\"\"\n", "        if self.monte_carlo_data is None:\n", "            self.generate_monte_carlo_data()\n", "\n", "        for param in self.params.keys():\n", "            # Pearson correlation\n", "            corr_coef, p_value = stats.pearsonr(\n", "                self.monte_carlo_data[param],\n", "                self.monte_carlo_data['Y']\n", "            )\n", "\n", "            # Spearman correlation (non-parametric)\n", "            spearman_coef, spearman_p = stats.spearmanr(\n", "                self.monte_carlo_data[param],\n", "                self.monte_carlo_data['Y']\n", "            )\n", "\n", "            self.statistical_tests[param] = {\n", "                'pearson_r': corr_coef,\n", "                'pearson_p': p_value,\n", "                'spearman_r': spearman_coef,\n", "                'spearman_p': spearman_p,\n", "                'significant': p_value < 0.05\n", "            }\n", "\n", "    def create_publication_plots(self):\n", "        \"\"\"Create publication-ready sensitivity plots\"\"\"\n", "        # Create a comprehensive figure with subplots\n", "        fig = plt.figure(figsize=(20, 16))\n", "        gs = fig.add_gridspec(4, 4, height_ratios=[1, 1, 1, 0.8], width_ratios=[1, 1, 1, 1])\n", "\n", "        # Individual parameter response curves\n", "        for i, (param, data) in enumerate(self.results.items()):\n", "            row, col = i // 2, (i % 2) * 2\n", "            ax = fig.add_subplot(gs[row, col:col+2])\n", "\n", "            # Main response curve\n", "            ax.plot(data['param_values'], data['y_values'],\n", "                   linewidth=3, alpha=0.8, label='Response Curve')\n", "\n", "            # Gradient visualization\n", "            ax2 = ax.twinx()\n", "            ax2.plot(data['param_values'], data['gradient'],\n", "                    color='red', alpha=0.6, linestyle='--',\n", "                    linewidth=2, label='Gradient (dY/dx)')\n", "\n", "            # Formatting\n", "            ax.set_xlabel(f\"{self.params[param]['name']} ({self.params[param]['unit']})\")\n", "            ax.set_ylabel('Output Y', color='blue')\n", "            ax2.set_ylabel('Gradient', color='red')\n", "            ax.set_title(f'{param}: Sensitivity Analysis\\n'\n", "                        f'SI = {data[\"sensitivity_index\"]:.3f}, R² = {data[\"linearity_r2\"]:.3f}',\n", "                        fontweight='bold')\n", "\n", "            # Add confidence intervals (approximation)\n", "            std_dev = data['std_output']\n", "            ax.fill_between(data['param_values'],\n", "                           data['y_values'] - std_dev/2,\n", "                           data['y_values'] + std_dev/2,\n", "                           alpha=0.2, label='±σ/2 Band')\n", "\n", "            ax.grid(True, alpha=0.3)\n", "            ax.legend(loc='upper left')\n", "            ax2.legend(loc='upper right')\n", "\n", "        # Summary sensitivity comparison\n", "        ax_summary = fig.add_subplot(gs[3, :])\n", "\n", "        params = list(self.results.keys())\n", "        sensitivity_indices = [self.results[p]['sensitivity_index'] for p in params]\n", "        sobol_indices = [self.results[p]['sobol_index'] for p in params]\n", "        r_squared = [self.results[p]['linearity_r2'] for p in params]\n", "\n", "        x = np.arange(len(params))\n", "        width = 0.25\n", "\n", "        bars1 = ax_summary.bar(x - width, sensitivity_indices, width,\n", "                              label='Sensitivity Index', alpha=0.8, color='skyblue')\n", "        bars2 = ax_summary.bar(x, sobol_indices, width,\n", "                              label='Sobol Index', alpha=0.8, color='lightcoral')\n", "        bars3 = ax_summary.bar(x + width, r_squared, width,\n", "                              label='Linearity (R²)', alpha=0.8, color='lightgreen')\n", "\n", "        # Add value labels\n", "        for bars in [bars1, bars2, bars3]:\n", "            for bar in bars:\n", "                height = bar.get_height()\n", "                ax_summary.text(bar.get_x() + bar.get_width()/2., height + 0.01,\n", "                               f'{height:.3f}', ha='center', va='bottom', fontsize=9)\n", "\n", "        ax_summary.set_xlabel('Parameters')\n", "        ax_summary.set_ylabel('Index Value')\n", "        ax_summary.set_title('Comparative Sensitivity Analysis', fontweight='bold')\n", "        ax_summary.set_xticks(x)\n", "        ax_summary.set_xticklabels([f\"{p}\\n({self.params[p]['unit']})\" for p in params])\n", "        ax_summary.legend()\n", "        ax_summary.grid(True, alpha=0.3)\n", "\n", "        plt.tight_layout()\n", "        plt.savefig('sensitivity_analysis.png', dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "\n", "    def create_advanced_correlation_analysis(self):\n", "        \"\"\"Create advanced correlation and interaction plots\"\"\"\n", "        if self.monte_carlo_data is None:\n", "            self.generate_monte_carlo_data()\n", "\n", "        # Create comprehensive correlation figure\n", "        fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "\n", "        # 1. Enhanced correlation matrix\n", "        ax1 = axes[0, 0]\n", "        corr_matrix = self.monte_carlo_data.corr()\n", "        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))\n", "\n", "        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='RdBu_r',\n", "                   center=0, square=True, linewidths=0.5, ax=ax1,\n", "                   cbar_kws={\"shrink\": .8}, fmt='.3f')\n", "        ax1.set_title('Correlation Matrix\\n(<PERSON>)', fontweight='bold')\n", "\n", "        # 2. Partial correlation analysis\n", "        ax2 = axes[0, 1]\n", "        # Calculate partial correlations (simplified approach)\n", "        y_values = self.monte_carlo_data['Y'].values\n", "        param_names = ['Tfeed', 'Xfeed', 'Ffeed', 'Tsteam']\n", "        partial_corrs = []\n", "\n", "        for param in param_names:\n", "            x = self.monte_carlo_data[param].values\n", "            corr, _ = stats.pearsonr(x, y_values)\n", "            partial_corrs.append(corr)\n", "\n", "        colors = ['red' if abs(c) > 0.5 else 'blue' for c in partial_corrs]\n", "        bars = ax2.bar(param_names, partial_corrs, color=colors, alpha=0.7)\n", "        ax2.set_title('Parameter-Output Correlations', fontweight='bold')\n", "        ax2.set_ylabel('Correlation Coefficient')\n", "        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "        ax2.grid(True, alpha=0.3)\n", "\n", "        # Add significance indicators\n", "        for i, (bar, param) in enumerate(zip(bars, param_names)):\n", "            if self.statistical_tests[param]['significant']:\n", "                ax2.text(bar.get_x() + bar.get_width()/2.,\n", "                        bar.get_height() + 0.02, '*',\n", "                        ha='center', va='bottom', fontsize=16, color='red')\n", "\n", "        # 3. Sensitivity tornado plot\n", "        ax3 = axes[0, 2]\n", "        params = list(self.results.keys())\n", "        sensitivities = [self.results[p]['sensitivity_index'] for p in params]\n", "        colors_tornado = plt.cm.RdYlBu_r(np.linspace(0.2, 0.8, len(params)))\n", "\n", "        y_pos = np.arange(len(params))\n", "        bars = ax3.barh(y_pos, sensitivities, color=colors_tornado, alpha=0.8)\n", "        ax3.set_yticks(y_pos)\n", "        ax3.set_yticklabels([f\"{p}\\n({self.params[p]['unit']})\" for p in params])\n", "        ax3.set_xlabel('Sensitivity Index')\n", "        ax3.set_title('Tornado Plot\\n(Parameter Importance)', fontweight='bold')\n", "        ax3.grid(True, alpha=0.3, axis='x')\n", "\n", "        # Add value labels\n", "        for i, (bar, val) in enumerate(zip(bars, sensitivities)):\n", "            ax3.text(val + 0.001, bar.get_y() + bar.get_height()/2,\n", "                    f'{val:.3f}', va='center', ha='left', fontsize=10)\n", "\n", "        # 4. <PERSON><PERSON><PERSON> plot matrix (selected pairs)\n", "        ax4 = axes[1, 0]\n", "        # Most influential parameter vs Y\n", "        most_influential = max(params, key=lambda p: self.results[p]['sensitivity_index'])\n", "        ax4.scatter(self.monte_carlo_data[most_influential],\n", "                   self.monte_carlo_data['Y'], alpha=0.5, s=20)\n", "        ax4.set_xlabel(f\"{self.params[most_influential]['name']} ({self.params[most_influential]['unit']})\")\n", "        ax4.set_ylabel('Output Y')\n", "        ax4.set_title(f'Most Influential: {most_influential}', fontweight='bold')\n", "\n", "        # Add regression line\n", "        z = np.polyfit(self.monte_carlo_data[most_influential],\n", "                      self.monte_carlo_data['Y'], 1)\n", "        p = np.poly1d(z)\n", "        ax4.plot(self.monte_carlo_data[most_influential],\n", "                p(self.monte_carlo_data[most_influential]),\n", "                \"r--\", alpha=0.8, linewidth=2)\n", "        ax4.grid(True, alpha=0.3)\n", "\n", "        # 5. Residual analysis\n", "        ax5 = axes[1, 1]\n", "        predicted_y = p(self.monte_carlo_data[most_influential])\n", "        residuals = self.monte_carlo_data['Y'] - predicted_y\n", "        ax5.scatter(predicted_y, residuals, alpha=0.5, s=20)\n", "        ax5.axhline(y=0, color='red', linestyle='--', alpha=0.8)\n", "        ax5.set_xlabel('Predicted Y')\n", "        ax5.set_ylabel('Residuals')\n", "        ax5.set_title('Residual Analysis', fontweight='bold')\n", "        ax5.grid(True, alpha=0.3)\n", "\n", "        # 6. Distribution of output Y\n", "        ax6 = axes[1, 2]\n", "        ax6.hist(self.monte_carlo_data['Y'], bins=50, alpha=0.7, density=True,\n", "                color='skyblue', edgecolor='black')\n", "\n", "        # Overlay normal distribution\n", "        mu, sigma = stats.norm.fit(self.monte_carlo_data['Y'])\n", "        x_norm = np.linspace(self.monte_carlo_data['Y'].min(),\n", "                           self.monte_carlo_data['Y'].max(), 100)\n", "        ax6.plot(x_norm, stats.norm.pdf(x_norm, mu, sigma),\n", "                'r-', linewidth=2, label=f'Normal (μ={mu:.2f}, σ={sigma:.2f})')\n", "\n", "        ax6.set_xlabel('Output Y')\n", "        ax6.set_ylabel('Density')\n", "        ax6.set_title('Output Distribution', fontweight='bold')\n", "        ax6.legend()\n", "        ax6.grid(True, alpha=0.3)\n", "\n", "        plt.tight_layout()\n", "        plt.savefig('advanced_correlation_analysis.png', dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "\n", "    def create_3d_interaction_surfaces(self):\n", "        \"\"\"Create advanced 3D interaction analysis\"\"\"\n", "        param_pairs = [\n", "            ('Tfeed', 'Xfeed'),\n", "            ('Ffeed', 'Xfeed'),\n", "            ('Tsteam', 'Tfeed')\n", "        ]\n", "\n", "        fig = plt.figure(figsize=(20, 6))\n", "\n", "        for i, (param1, param2) in enumerate(param_pairs):\n", "            ax = fig.add_subplot(1, 3, i+1, projection='3d')\n", "\n", "            # Create parameter grids\n", "            p1_range = np.linspace(self.params[param1]['min'],\n", "                                 self.params[param1]['max'], 40)\n", "            p2_range = np.linspace(self.params[param2]['min'],\n", "                                 self.params[param2]['max'], 40)\n", "            P1, P2 = np.meshgrid(p1_range, p2_range)\n", "\n", "            # Fix other parameters at midpoints\n", "            fixed_params = {p: self.midpoints[p] for p in self.params.keys()\n", "                           if p not in [param1, param2]}\n", "\n", "            # Calculate Y surface\n", "            Y_surface = np.zeros_like(P1)\n", "            for j in range(P1.shape[0]):\n", "                for k in range(P1.shape[1]):\n", "                    params_dict = fixed_params.copy()\n", "                    params_dict[param1] = P1[j, k]\n", "                    params_dict[param2] = P2[j, k]\n", "                    Y_surface[j, k] = self.calculate_y(**params_dict)\n", "\n", "            # Create surface plot with enhanced styling\n", "            surf = ax.plot_surface(P1, P2, Y_surface, cmap='viridis',\n", "                                 alpha=0.9, linewidth=0, antialiased=True)\n", "\n", "            # Add contour lines at the bottom\n", "            ax.contour(P1, P2, Y_surface, zdir='z',\n", "                      offset=Y_surface.min()-1, cmap='viridis', alpha=0.5)\n", "\n", "            ax.set_xlabel(f\"{self.params[param1]['name']}\\n({self.params[param1]['unit']})\")\n", "            ax.set_ylabel(f\"{self.params[param2]['name']}\\n({self.params[param2]['unit']})\")\n", "            ax.set_zlabel('Output Y')\n", "            ax.set_title(f'3D Interaction Surface\\n{param1} vs {param2}',\n", "                        fontweight='bold')\n", "\n", "            # Improve viewing angle\n", "            ax.view_init(elev=20, azim=45)\n", "\n", "        plt.tight_layout()\n", "        plt.savefig('3d_interaction_surfaces.png', dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "\n", "    def create_interactive_plotly_dashboard(self):\n", "        \"\"\"Create interactive Plotly dashboard for web-based analysis\"\"\"\n", "        # Create subplots\n", "        fig = make_subplots(\n", "            rows=3, cols=2,\n", "            subplot_titles=('Parameter Response Curves', 'Sensitivity Comparison',\n", "                           'Correlation Heatmap', '3D Parameter Interaction',\n", "                           '<PERSON>', 'Statistical Summary'),\n", "            specs=[[{\"secondary_y\": False}, {\"secondary_y\": False}],\n", "                   [{\"secondary_y\": False}, {\"type\": \"scene\"}],\n", "                   [{\"secondary_y\": False}, {\"secondary_y\": False}]]\n", "        )\n", "\n", "        # 1. Parameter response curves\n", "        colors = px.colors.qualitative.Set1\n", "        for i, (param, data) in enumerate(self.results.items()):\n", "            fig.add_trace(\n", "                go.<PERSON>er(x=data['param_values'], y=data['y_values'],\n", "                          mode='lines', name=param, line=dict(width=3, color=colors[i])),\n", "                row=1, col=1\n", "            )\n", "\n", "        # 2. Sensitivity comparison\n", "        params = list(self.results.keys())\n", "        sensitivity_values = [self.results[p]['sensitivity_index'] for p in params]\n", "        fig.add_trace(\n", "            go.Bar(x=params, y=sensitivity_values,\n", "                  marker_color=colors[:len(params)],\n", "                  name='Sensitivity Index'),\n", "            row=1, col=2\n", "        )\n", "\n", "        # 3. Correlation heatmap\n", "        if self.monte_carlo_data is not None:\n", "            corr_matrix = self.monte_carlo_data.corr()\n", "            fig.add_trace(\n", "                go.Heatmap(z=corr_matrix.values,\n", "                          x=corr_matrix.columns,\n", "                          y=corr_matrix.columns,\n", "                          colorscale='RdBu',\n", "                          zmid=0),\n", "                row=2, col=1\n", "            )\n", "\n", "        # 4. 3D interaction surface (example with two most influential parameters)\n", "        if len(params) >= 2:\n", "            sorted_params = sorted(params,\n", "                                 key=lambda p: self.results[p]['sensitivity_index'],\n", "                                 reverse=True)\n", "            param1, param2 = sorted_params[:2]\n", "\n", "            p1_range = np.linspace(self.params[param1]['min'],\n", "                                 self.params[param1]['max'], 20)\n", "            p2_range = np.linspace(self.params[param2]['min'],\n", "                                 self.params[param2]['max'], 20)\n", "            P1, P2 = np.meshgrid(p1_range, p2_range)\n", "\n", "            fixed_params = {p: self.midpoints[p] for p in self.params.keys()\n", "                           if p not in [param1, param2]}\n", "\n", "            Y_surface = np.zeros_like(P1)\n", "            for j in range(P1.shape[0]):\n", "                for k in range(P1.shape[1]):\n", "                    params_dict = fixed_params.copy()\n", "                    params_dict[param1] = P1[j, k]\n", "                    params_dict[param2] = P2[j, k]\n", "                    Y_surface[j, k] = self.calculate_y(**params_dict)\n", "\n", "            fig.add_trace(\n", "                go.Surface(x=P1, y=P2, z=Y_surface, colorscale='viridis'),\n", "                row=2, col=2\n", "            )\n", "\n", "        # 5. <PERSON> scatter\n", "        if self.monte_carlo_data is not None:\n", "            most_influential = max(params, key=lambda p: self.results[p]['sensitivity_index'])\n", "            fig.add_trace(\n", "                go.<PERSON>(x=self.monte_carlo_data[most_influential],\n", "                          y=self.monte_carlo_data['Y'],\n", "                          mode='markers', name=f'{most_influential} vs Y',\n", "                          marker=dict(size=3, opacity=0.6)),\n", "                row=3, col=1\n", "            )\n", "\n", "        # Update layout\n", "        fig.update_layout(\n", "            height=1200,\n", "            title_text=\"Advanced Sensitivity Analysis Dashboard\",\n", "            title_x=0.5,\n", "            showlegend=True\n", "        )\n", "\n", "        # Update axes labels\n", "        fig.update_xaxes(title_text=\"Parameter Value\", row=1, col=1)\n", "        fig.update_yaxes(title_text=\"Output Y\", row=1, col=1)\n", "        fig.update_xaxes(title_text=\"Parameters\", row=1, col=2)\n", "        fig.update_yaxes(title_text=\"Sensitivity Index\", row=1, col=2)\n", "\n", "        # Save as HTML\n", "        fig.write_html(\"interactive_sensitivity_dashboard.html\")\n", "        fig.show()\n", "\n", "    def generate_comprehensive_report(self):\n", "        \"\"\"Generate a comprehensive statistical and technical report\"\"\"\n", "        report = []\n", "        report.append(\"=\" * 80)\n", "        report.append(\"COMPREHENSIVE SENSITIVITY ANALYSIS REPORT\")\n", "        report.append(\"=\" * 80)\n", "        report.append(f\"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "        report.append(f\"<PERSON>: {self.n_samples_monte_carlo:,}\")\n", "        report.append(f\"Univariate Steps: {self.n_steps}\")\n", "        report.append(\"\")\n", "\n", "        # Process equation\n", "        report.append(\"PROCESS EQUATION:\")\n", "        report.append(\"Y = (Ffeed × Xfeed) + (Tsteam − Tfeed)\")\n", "        report.append(\"\")\n", "\n", "        # Parameter summary\n", "        report.append(\"PARAMETER SPECIFICATIONS:\")\n", "        report.append(\"-\" * 50)\n", "        for param, info in self.params.items():\n", "            report.append(f\"{param:8s}: {info['min']:6.2f} - {info['max']:6.2f} {info['unit']:10s} | {info['description']}\")\n", "        report.append(\"\")\n", "\n", "        # Statistical summary\n", "        if self.monte_carlo_data is not None:\n", "            y_stats = self.monte_carlo_data['Y'].describe()\n", "            report.append(\"OUTPUT STATISTICS (Monte Carlo Analysis):\")\n", "            report.append(\"-\" * 50)\n", "            report.append(f\"Mean (μ):       {y_stats['mean']:10.4f}\")\n", "            report.append(f\"Std Dev (σ):    {y_stats['std']:10.4f}\")\n", "            report.append(f\"Minimum:        {y_stats['min']:10.4f}\")\n", "            report.append(f\"Maximum:        {y_stats['max']:10.4f}\")\n", "            report.append(f\"Range:          {y_stats['max'] - y_stats['min']:10.4f}\")\n", "            report.append(f\"CV (%):         {(y_stats['std']/y_stats['mean']*100):10.2f}\")\n", "            report.append(\"\")\n", "\n", "        # Sensitivity ranking\n", "        sorted_params = sorted(self.results.items(),\n", "                             key=lambda x: x[1]['sensitivity_index'],\n", "                             reverse=True)\n", "\n", "        report.append(\"PARAMETER SENSITIVITY RANKING:\")\n", "        report.append(\"-\" * 50)\n", "        report.append(f\"{'Rank':<4} {'Parameter':<8} {'Sens.Index':<12} {'R²':<8} {'Correlation':<12} {'P-value':<10}\")\n", "        report.append(\"-\" * 50)\n", "\n", "        for i, (param, data) in enumerate(sorted_params, 1):\n", "            corr = self.statistical_tests[param]['pearson_r']\n", "            p_val = self.statistical_tests[param]['pearson_p']\n", "            significance = \"***\" if p_val < 0.001 else \"**\" if p_val < 0.01 else \"*\" if p_val < 0.05 else \"\"\n", "\n", "            report.append(f\"{i:<4} {param:<8} {data['sensitivity_index']:<12.4f} \"\n", "                         f\"{data['linearity_r2']:<8.4f} {corr:<12.4f} \"\n", "                         f\"{p_val:<10.4e} {significance}\")\n", "\n", "        report.append(\"\")\n", "        report.append(\"Significance levels: *** p<0.001, ** p<0.01, * p<0.05\")\n", "        report.append(\"\")\n", "\n", "        # Detailed parameter analysis\n", "        report.append(\"DETAILED PARAMETER ANALYSIS:\")\n", "        report.append(\"=\" * 50)\n", "\n", "        for param, data in self.results.items():\n", "            report.append(f\"\\n{param.upper()} ({self.params[param]['name']}):\")\n", "            report.append(\"-\" * 30)\n", "            report.append(f\"Operating Range:    {self.params[param]['min']} - {self.params[param]['max']} {self.params[param]['unit']}\")\n", "            report.append(f\"Sensitivity Index:  {data['sensitivity_index']:.4f}\")\n", "            report.append(f\"Total Change:       {data['total_change']:.4f}\")\n", "            report.append(f\"Relative Change:    {data['relative_change']:.2f}%\")\n", "            report.append(f\"Average Gradient:   {data['avg_gradient']:.4f}\")\n", "            report.append(f\"Linearity (R²):     {data['linearity_r2']:.4f}\")\n", "            report.append(f\"Correlation:        {self.statistical_tests[param]['pearson_r']:.4f}\")\n", "            report.append(f\"P-value:            {self.statistical_tests[param]['pearson_p']:.4e}\")\n", "            report.append(f\"Output Range:       {data['output_range'][0]:.4f} - {data['output_range'][1]:.4f}\")\n", "            report.append(f\"Coefficient of Var: {data['cv']:.2f}%\")\n", "\n", "        report.append(\"\")\n", "        report.append(\"RECOMMENDATIONS:\")\n", "        report.append(\"-\" * 50)\n", "\n", "        # Generate recommendations based on sensitivity analysis\n", "        most_sensitive = max(self.results.items(), key=lambda x: x[1]['sensitivity_index'])\n", "        least_sensitive = min(self.results.items(), key=lambda x: x[1]['sensitivity_index'])\n", "\n", "        report.append(f\"1. Most Critical Parameter: {most_sensitive[0]} (SI = {most_sensitive[1]['sensitivity_index']:.4f})\")\n", "        report.append(f\"   - Requires tight control and monitoring\")\n", "        report.append(f\"   - Small changes cause significant output variation\")\n", "        report.append(\"\")\n", "        report.append(f\"2. Least Critical Parameter: {least_sensitive[0]} (SI = {least_sensitive[1]['sensitivity_index']:.4f})\")\n", "        report.append(f\"   - More tolerance in operational control\")\n", "        report.append(f\"   - Lower priority for precision instrumentation\")\n", "        report.append(\"\")\n", "\n", "        # Control recommendations\n", "        for param, data in sorted(self.results.items(), key=lambda x: x[1]['sensitivity_index'], reverse=True):\n", "            if data['sensitivity_index'] > 1.0:\n", "                report.append(f\"3. {param}: HIGH PRIORITY CONTROL\")\n", "                report.append(f\"   - Implement advanced control strategies\")\n", "                report.append(f\"   - Use high-precision sensors\")\n", "                report.append(f\"   - Consider cascade or feedforward control\")\n", "            elif data['sensitivity_index'] > 0.5:\n", "                report.append(f\"4. {param}: MEDIUM PRIORITY CONTROL\")\n", "                report.append(f\"   - Standard PID control adequate\")\n", "                report.append(f\"   - Regular calibration recommended\")\n", "            else:\n", "                report.append(f\"5. {param}: LOW PRIORITY CONTROL\")\n", "                report.append(f\"   - Basic control sufficient\")\n", "                report.append(f\"   - Focus on other parameters\")\n", "            report.append(\"\")\n", "\n", "        report.append(\"=\" * 80)\n", "        report.append(\"END OF REPORT\")\n", "        report.append(\"=\" * 80)\n", "\n", "        # Save report to file\n", "        with open('sensitivity_analysis_report.txt', 'w') as f:\n", "            f.write('\\n'.join(report))\n", "\n", "        # Print report\n", "        for line in report:\n", "            print(line)\n", "\n", "        return report\n", "\n", "    def create_additional_charts(self):\n", "        \\\"\\\"\\\"Create additional specialized charts for comprehensive analysis\\\"\\\"\\\"\n", "        fig, axes = plt.subplots(3, 3, figsize=(20, 18))\n", "        fig.suptitle('Advanced Sensitivity Analysis - Additional Charts', fontsize=16, fontweight='bold')\n", "\n", "        # 1. Parameter Importance Pie Chart\n", "        ax1 = axes[0, 0]\n", "        params = list(self.results.keys())\n", "        sensitivities = [self.results[p]['sensitivity_index'] for p in params]\n", "        colors = plt.cm.Set3(np.linspace(0, 1, len(params)))\n", "        \n", "        wedges, texts, autotexts = ax1.pie(sensitivities, labels=params, autopct='%1.1f%%',\n", "                                          colors=colors, startangle=90, explode=[0.05]*len(params))\n", "        ax1.set_title('Parameter Sensitivity Distribution', fontweight='bold')\n", "        \n", "        # 2. Box Plot of Parameter Variations\n", "        ax2 = axes[0, 1]\n", "        if self.monte_carlo_data is not None:\n", "            param_data = [self.monte_carlo_data[p] for p in params]\n", "            bp = ax2.boxplot(param_data, labels=params, patch_artist=True)\n", "            for patch, color in zip(bp['boxes'], colors):\n", "                patch.set_facecolor(color)\n", "        ax2.set_title('Parameter Value Distributions', fontweight='bold')\n", "        ax2.set_ylabel('Parameter Values')\n", "        ax2.tick_params(axis='x', rotation=45)\n", "\n", "        # 3. Gradient Analysis\n", "        ax3 = axes[0, 2]\n", "        for i, (param, data) in enumerate(self.results.items()):\n", "            ax3.plot(data['param_values'], np.abs(data['gradient']), \n", "                    label=param, linewidth=2, color=colors[i])\n", "        ax3.set_title('Absolute Gradient Analysis', fontweight='bold')\n", "        ax3.set_xlabel('Parameter Value')\n", "        ax3.set_ylabel('|dY/dx|')\n", "        ax3.legend()\n", "        ax3.grid(True, alpha=0.3)\n", "\n", "        # 4. Sensitivity vs Linearity Scatter\n", "        ax4 = axes[1, 0]\n", "        sens_values = [self.results[p]['sensitivity_index'] for p in params]\n", "        r2_values = [self.results[p]['linearity_r2'] for p in params]\n", "        scatter = ax4.scatter(sens_values, r2_values, c=range(len(params)), \n", "                             cmap='viridis', s=100, alpha=0.7)\n", "        for i, param in enumerate(params):\n", "            ax4.annotate(param, (sens_values[i], r2_values[i]), \n", "                        xytext=(5, 5), textcoords='offset points')\n", "        ax4.set_xlabel('Sensitivity Index')\n", "        ax4.set_ylabel('Linearity (R²)')\n", "        ax4.set_title('Sensitivity vs Linearity', fontweight='bold')\n", "        ax4.grid(True, alpha=0.3)\n", "\n", "        # 5. Output Variance Decomposition\n", "        ax5 = axes[1, 1]\n", "        sobol_indices = [self.results[p]['sobol_index'] for p in params]\n", "        bars = ax5.bar(params, sobol_indices, color=colors, alpha=0.8)\n", "        ax5.set_title('Sobol Sensitivity Indices', fontweight='bold')\n", "        ax5.set_ylabel('Sobol Index')\n", "        ax5.tick_params(axis='x', rotation=45)\n", "        \n", "        # Add value labels on bars\n", "        for bar, val in zip(bars, sobol_indices):\n", "            ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,\n", "                    f'{val:.3f}', ha='center', va='bottom')\n", "\n", "        # 6. Parameter Correlation Network\n", "        ax6 = axes[1, 2]\n", "        if self.monte_carlo_data is not None:\n", "            corr_matrix = self.monte_carlo_data[params].corr()\n", "            im = ax6.imshow(corr_matrix, cmap='RdBu_r', vmin=-1, vmax=1)\n", "            ax6.set_xticks(range(len(params)))\n", "            ax6.set_yticks(range(len(params)))\n", "            ax6.set_xticklabels(params, rotation=45)\n", "            ax6.set_yticklabels(params)\n", "            \n", "            # Add correlation values\n", "            for i in range(len(params)):\n", "                for j in range(len(params)):\n", "                    text = ax6.text(j, i, f'{corr_matrix.iloc[i, j]:.2f}',\n", "                                   ha='center', va='center', color='black')\n", "        ax6.set_title('Parameter Correlation Matrix', fontweight='bold')\n", "        plt.colorbar(im, ax=ax6, shrink=0.8)\n", "\n", "        # 7. Cumulative Sensitivity\n", "        ax7 = axes[2, 0]\n", "        sorted_params = sorted(params, key=lambda p: self.results[p]['sensitivity_index'], reverse=True)\n", "        sorted_sens = [self.results[p]['sensitivity_index'] for p in sorted_params]\n", "        cumulative_sens = np.cumsum(sorted_sens) / np.sum(sorted_sens) * 100\n", "        \n", "        ax7.plot(range(1, len(sorted_params)+1), cumulative_sens, 'o-', linewidth=3, markersize=8)\n", "        ax7.axhline(y=80, color='red', linestyle='--', alpha=0.7, label='80% Threshold')\n", "        ax7.set_xlabel('Parameter Rank')\n", "        ax7.set_ylabel('Cumulative Sensitivity (%)')\n", "        ax7.set_title('Cumulative Sensitivity Analysis', fontweight='bold')\n", "        ax7.set_xticks(range(1, len(sorted_params)+1))\n", "        ax7.set_xticklabels(sorted_params, rotation=45)\n", "        ax7.legend()\n", "        ax7.grid(True, alpha=0.3)\n", "\n", "        # 8. Statistical Significance Plot\n", "        ax8 = axes[2, 1]\n", "        p_values = [self.statistical_tests[p]['pearson_p'] for p in params]\n", "        correlations = [self.statistical_tests[p]['pearson_r'] for p in params]\n", "        \n", "        # Color by significance\n", "        colors_sig = ['red' if p < 0.001 else 'orange' if p < 0.01 else 'yellow' if p < 0.05 else 'gray' \n", "                     for p in p_values]\n", "        \n", "        bars = ax8.bar(params, np.abs(correlations), color=colors_sig, alpha=0.8)\n", "        ax8.axhline(y=0.5, color='red', linestyle='--', alpha=0.7, label='Strong Correlation')\n", "        ax8.set_title('Statistical Significance of Correlations', fontweight='bold')\n", "        ax8.set_ylabel('|Correlation Coefficient|')\n", "        ax8.tick_params(axis='x', rotation=45)\n", "        ax8.legend()\n", "        ax8.grid(True, alpha=0.3)\n", "\n", "        # 9. Risk Assessment Matrix\n", "        ax9 = axes[2, 2]\n", "        impact = [self.results[p]['relative_change'] for p in params]\n", "        uncertainty = [self.results[p]['cv'] for p in params]  # Coefficient of variation as uncertainty\n", "        \n", "        scatter = ax9.scatter(uncertainty, impact, c=sens_values, cmap='Reds', s=150, alpha=0.8)\n", "        for i, param in enumerate(params):\n", "            ax9.annotate(param, (uncertainty[i], impact[i]), \n", "                        xytext=(5, 5), textcoords='offset points')\n", "        \n", "        ax9.axhline(y=np.mean(impact), color='blue', linestyle='--', alpha=0.5, label='Mean Impact')\n", "        ax9.axvline(x=np.mean(uncertainty), color='blue', linestyle='--', alpha=0.5, label='Mean Uncertainty')\n", "        ax9.set_xlabel('Uncertainty (CV %)')\n", "        ax9.set_ylabel('Impact (Relative Change %)')\n", "        ax9.set_title('Risk Assessment Matrix', fontweight='bold')\n", "        ax9.legend()\n", "        ax9.grid(True, alpha=0.3)\n", "        plt.colorbar(scatter, ax=ax9, label='Sensitivity Index')\n", "\n", "        plt.tight_layout()\n", "        plt.savefig('additional_sensitivity_charts.png', dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "\n", "    def run_complete_analysis(self):\n", "        \\\"\\\"\\\"Run the complete enhanced sensitivity analysis\\\"\\\"\\\"\n", "        print(\\\"Starting Enhanced Sensitivity Analysis...\\\")\n", "        print(\\\"=\\\" * 60)\n", "        \n", "        # Run comprehensive analysis\n", "        self.run_comprehensive_analysis()\n", "        \n", "        # Create all visualizations\n", "        print(\\\"\\nGenerating publication-ready plots...\\\")\n", "        self.create_publication_plots()\n", "        \n", "        print(\\\"\\nGenerating advanced correlation analysis...\\\")\n", "        self.create_advanced_correlation_analysis()\n", "        \n", "        print(\\\"\\nGenerating 3D interaction surfaces...\\\")\n", "        self.create_3d_interaction_surfaces()\n", "        \n", "        print(\\\"\\nGenerating additional specialized charts...\\\")\n", "        self.create_additional_charts()\n", "        \n", "        print(\\\"\\nGenerating interactive dashboard...\\\")\n", "        self.create_interactive_plotly_dashboard()\n", "        \n", "        print(\\\"\\nGenerating comprehensive report...\\\")\n", "        self.generate_comprehensive_report()\n", "        \n", "        print(\\\"\\nAnalysis Complete! Files generated:\\\")  \n", "        print(\\\"- sensitivity_analysis.png\\\")\n", "        print(\\\"- advanced_correlation_analysis.png\\\")\n", "        print(\\\"- 3d_interaction_surfaces.png\\\")\n", "        print(\\\"- additional_sensitivity_charts.png\\\")\n", "        print(\\\"- interactive_sensitivity_dashboard.html\\\")\n", "        print(\\\"- sensitivity_analysis_report.txt\\\")\n", "        \n", "        return self.results\n", "\n", "# Execute the enhanced analysis\n", "if __name__ == \\\"__main__\\\":\n", "    # Create analyzer instance\n", "    analyzer = AdvancedSensitivityAnalysis()\n", "    \n", "    # Run complete analysis\n", "    results = analyzer.run_complete_analysis()\n", "    \n", "    # Display summary\n", "    print(\\\"\\n\\\" + \\\"=\\\" * 60)\n", "    print(\\\"ANALYSIS SUMMARY\\\")\n", "    print(\\\"=\\\" * 60)\n", "    \n", "    for param, data in sorted(results.items(), key=lambda x: x[1]['sensitivity_index'], reverse=True):\n", "        print(f\\\"{param:8s}: SI = {data['sensitivity_index']:.4f}, R² = {data['linearity_r2']:.4f}, \\\"\n", "              f\\\"Corr = {analyzer.statistical_tests[param]['pearson_r']:.4f}\\\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "run_analysis"}, "outputs": [], "source": ["# Run the enhanced sensitivity analysis\n", "analyzer = AdvancedSensitivityAnalysis()\n", "results = analyzer.run_complete_analysis()"]}]}